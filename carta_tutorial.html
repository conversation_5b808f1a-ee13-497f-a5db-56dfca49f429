<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CARTA: Cell Differentiation Mapping Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --light-bg: #ecf0f1;
            --dark-bg: #34495e;
            --text-color: #2c3e50;
            --border-color: #bdc3c7;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-bg) 100%);
            color: white;
            padding: 2rem 0;
            text-align: center;
            margin-bottom: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .nav-menu {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 20px;
            z-index: 100;
        }

        .nav-menu h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 0.5rem;
        }

        .nav-menu ul {
            list-style: none;
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .nav-menu a {
            color: var(--secondary-color);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .nav-menu a:hover {
            background: var(--secondary-color);
            color: white;
            transform: translateY(-2px);
        }

        .section {
            background: white;
            margin-bottom: 2rem;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
            border-left: 4px solid var(--secondary-color);
        }

        .section h2 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
            border-bottom: 2px solid var(--light-bg);
            padding-bottom: 0.5rem;
        }

        .section h3 {
            color: var(--secondary-color);
            margin: 1.5rem 0 1rem 0;
            font-size: 1.4rem;
        }

        .highlight-box {
            background: linear-gradient(135deg, var(--light-bg) 0%, #ffffff 100%);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 4px solid var(--accent-color);
        }

        .key-concept {
            background: linear-gradient(135deg, #e8f5e8 0%, #ffffff 100%);
            border-left: 4px solid var(--success-color);
        }

        .warning-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%);
            border-left: 4px solid var(--warning-color);
        }

        .math-formula {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: center;
            font-size: 1.1rem;
        }

        .step-number {
            background: var(--secondary-color);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }

        .algorithm-box {
            background: #f8f9fa;
            border: 2px solid var(--secondary-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .svg-container {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .nav-menu ul {
                flex-direction: column;
            }
            
            .section {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>CARTA Tutorial</h1>
            <p class="subtitle">Cell Differentiation Mapping from Lineage Tracing Data</p>
        </header>

        <nav class="nav-menu">
            <h3>📚 Tutorial Outline</h3>
            <ul>
                <li><a href="#introduction">1. Introduction</a></li>
                <li><a href="#mathematical-framework">2. Mathematical Framework</a></li>
                <li><a href="#potency-concept">3. Potency Concept</a></li>
                <li><a href="#algorithm">4. CARTA Algorithm</a></li>
                <li><a href="#discrepancy-score">5. Discrepancy Score</a></li>
                <li><a href="#pareto-optimization">6. Pareto Optimization</a></li>
                <li><a href="#implementation">7. Implementation Steps</a></li>
                <li><a href="#applications">8. Real-world Applications</a></li>
                <li><a href="#limitations">9. Limitations & Future Work</a></li>
            </ul>
        </nav>

        <section id="introduction" class="section">
            <h2>🧬 1. Introduction to CARTA</h2>
            
            <div class="highlight-box key-concept">
                <h3>What is CARTA?</h3>
                <p><strong>CARTA</strong> (Cell Differentiation Mapping) is a computational algorithm that infers optimal cell differentiation maps from single-cell lineage tracing data. It addresses the fundamental challenge of understanding how multipotent cells differentiate through hierarchies of increasingly restricted progenitor cell types.</p>
            </div>

            <h3>🎯 Key Problems CARTA Solves</h3>
            <ul>
                <li><strong>Unobserved Progenitors:</strong> Many progenitor cell types exist during development but are not captured in experimental data</li>
                <li><strong>Complex Differentiation Patterns:</strong> Real development involves convergent differentiation and non-tree structures</li>
                <li><strong>Limited Sampling:</strong> Current lineage tracing technologies have throughput limitations</li>
                <li><strong>Trade-off Optimization:</strong> Balancing model complexity vs. data fit</li>
            </ul>

            <div class="svg-container">
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- Background -->
                    <defs>
                        <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="600" height="300" fill="url(#bgGrad)" rx="10"/>
                    
                    <!-- Title -->
                    <text x="300" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Cell Differentiation Mapping Challenge</text>
                    
                    <!-- Input: Lineage Trees -->
                    <g transform="translate(50, 60)">
                        <rect x="0" y="0" width="120" height="80" fill="#3498db" rx="5" opacity="0.8"/>
                        <text x="60" y="25" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Input:</text>
                        <text x="60" y="40" text-anchor="middle" font-size="11" fill="white">Lineage Trees</text>
                        <text x="60" y="55" text-anchor="middle" font-size="10" fill="white">• Limited sampling</text>
                        <text x="60" y="70" text-anchor="middle" font-size="10" fill="white">• Unlabeled ancestors</text>
                    </g>
                    
                    <!-- Arrow -->
                    <path d="M 180 100 L 220 100" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                        </marker>
                    </defs>
                    
                    <!-- CARTA Process -->
                    <g transform="translate(230, 60)">
                        <rect x="0" y="0" width="140" height="80" fill="#e74c3c" rx="5" opacity="0.8"/>
                        <text x="70" y="25" text-anchor="middle" font-size="12" fill="white" font-weight="bold">CARTA Algorithm</text>
                        <text x="70" y="40" text-anchor="middle" font-size="10" fill="white">• Potency modeling</text>
                        <text x="70" y="52" text-anchor="middle" font-size="10" fill="white">• Discrepancy scoring</text>
                        <text x="70" y="64" text-anchor="middle" font-size="10" fill="white">• Pareto optimization</text>
                    </g>
                    
                    <!-- Arrow -->
                    <path d="M 380 100 L 420 100" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>
                    
                    <!-- Output: Differentiation Map -->
                    <g transform="translate(430, 60)">
                        <rect x="0" y="0" width="120" height="80" fill="#27ae60" rx="5" opacity="0.8"/>
                        <text x="60" y="25" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Output:</text>
                        <text x="60" y="40" text-anchor="middle" font-size="11" fill="white">Differentiation Map</text>
                        <text x="60" y="55" text-anchor="middle" font-size="10" fill="white">• Inferred progenitors</text>
                        <text x="60" y="70" text-anchor="middle" font-size="10" fill="white">• Optimal complexity</text>
                    </g>
                    
                    <!-- Bottom illustration -->
                    <g transform="translate(100, 180)">
                        <text x="200" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Key Innovation: Potency-Based Modeling</text>
                        
                        <!-- Observed cells -->
                        <circle cx="50" cy="50" r="15" fill="#3498db"/>
                        <text x="50" y="55" text-anchor="middle" font-size="10" fill="white">A</text>
                        <text x="50" y="75" text-anchor="middle" font-size="9" fill="#2c3e50">Observed</text>
                        
                        <circle cx="150" cy="50" r="15" fill="#3498db"/>
                        <text x="150" y="55" text-anchor="middle" font-size="10" fill="white">B</text>
                        <text x="150" y="75" text-anchor="middle" font-size="9" fill="#2c3e50">Observed</text>
                        
                        <circle cx="250" cy="50" r="15" fill="#3498db"/>
                        <text x="250" y="55" text-anchor="middle" font-size="10" fill="white">C</text>
                        <text x="250" y="75" text-anchor="middle" font-size="9" fill="#2c3e50">Observed</text>
                        
                        <!-- Unobserved progenitor -->
                        <ellipse cx="150" cy="100" rx="25" ry="15" fill="#e74c3c" opacity="0.7"/>
                        <text x="150" y="105" text-anchor="middle" font-size="9" fill="white">{A,B,C}</text>
                        <text x="150" y="125" text-anchor="middle" font-size="9" fill="#2c3e50">Unobserved</text>
                        <text x="150" y="135" text-anchor="middle" font-size="9" fill="#2c3e50">Progenitor</text>
                        
                        <!-- Connections -->
                        <path d="M 135 85 L 65 65" stroke="#666" stroke-width="2"/>
                        <path d="M 150 85 L 150 65" stroke="#666" stroke-width="2"/>
                        <path d="M 165 85 L 235 65" stroke="#666" stroke-width="2"/>
                    </g>
                </svg>
            </div>
        </section>

        <section id="mathematical-framework" class="section">
            <h2>🔢 2. Mathematical Framework</h2>

            <div class="highlight-box key-concept">
                <h3>Core Mathematical Concepts</h3>
                <p>CARTA represents cell differentiation maps as <strong>directed acyclic graphs (DAGs)</strong> where vertices are cell types and edges represent differentiation transitions.</p>
            </div>

            <h3>📊 Formal Definitions</h3>

            <div class="math-formula">
                <h4>Cell Lineage Trees</h4>
                <p>Input: $T := \{T_1, T_2, \ldots, T_m\}$ where each $T_i$ is a lineage tree</p>
                <ul style="text-align: left; margin-top: 1rem;">
                    <li>Leaves = sequenced cells (labeled with cell types)</li>
                    <li>Internal vertices = ancestral cells (unlabeled)</li>
                    <li>Edges = cell divisions</li>
                </ul>
            </div>

            <div class="math-formula">
                <h4>Observed Cell Types</h4>
                <p>$S$ = set of observed cell types that label the leaves of $T$</p>
                <p>Example: $S = \{A, B, C\}$ for three observed cell types</p>
            </div>

            <div class="math-formula">
                <h4>Cell Differentiation Map</h4>
                <p>$F_S$ = directed graph where:</p>
                <ul style="text-align: left; margin-top: 1rem;">
                    <li>Vertices represent cell types (observed + unobserved progenitors)</li>
                    <li>Edges represent cell type transitions</li>
                    <li>Each vertex is labeled by either an element of $S$ or a subset of $S$</li>
                </ul>
            </div>

            <div class="svg-container">
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Background -->
                    <rect width="700" height="400" fill="url(#bgGrad)" rx="10"/>

                    <!-- Title -->
                    <text x="350" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Mathematical Framework Visualization</text>

                    <!-- Left side: Lineage Tree -->
                    <g transform="translate(50, 60)">
                        <text x="100" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Input: Lineage Tree T</text>

                        <!-- Tree structure -->
                        <circle cx="100" cy="50" r="12" fill="#95a5a6" stroke="#2c3e50" stroke-width="2"/>
                        <text x="100" y="55" text-anchor="middle" font-size="10" fill="white">?</text>
                        <text x="100" y="75" text-anchor="middle" font-size="9" fill="#2c3e50">Unlabeled</text>
                        <text x="100" y="85" text-anchor="middle" font-size="9" fill="#2c3e50">Ancestor</text>

                        <!-- Child nodes -->
                        <circle cx="60" cy="120" r="12" fill="#95a5a6" stroke="#2c3e50" stroke-width="2"/>
                        <text x="60" y="125" text-anchor="middle" font-size="10" fill="white">?</text>

                        <circle cx="140" cy="120" r="12" fill="#95a5a6" stroke="#2c3e50" stroke-width="2"/>
                        <text x="140" y="125" text-anchor="middle" font-size="10" fill="white">?</text>

                        <!-- Leaf nodes (observed) -->
                        <circle cx="30" cy="180" r="12" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                        <text x="30" y="185" text-anchor="middle" font-size="10" fill="white">A</text>
                        <text x="30" y="200" text-anchor="middle" font-size="9" fill="#2c3e50">Observed</text>

                        <circle cx="90" cy="180" r="12" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                        <text x="90" y="185" text-anchor="middle" font-size="10" fill="white">B</text>
                        <text x="90" y="200" text-anchor="middle" font-size="9" fill="#2c3e50">Observed</text>

                        <circle cx="170" cy="180" r="12" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                        <text x="170" y="185" text-anchor="middle" font-size="10" fill="white">C</text>
                        <text x="170" y="200" text-anchor="middle" font-size="9" fill="#2c3e50">Observed</text>

                        <!-- Tree edges -->
                        <path d="M 100 62 L 60 108" stroke="#2c3e50" stroke-width="2"/>
                        <path d="M 100 62 L 140 108" stroke="#2c3e50" stroke-width="2"/>
                        <path d="M 60 132 L 30 168" stroke="#2c3e50" stroke-width="2"/>
                        <path d="M 60 132 L 90 168" stroke="#2c3e50" stroke-width="2"/>
                        <path d="M 140 132 L 170 168" stroke="#2c3e50" stroke-width="2"/>
                    </g>

                    <!-- Arrow -->
                    <path d="M 250 200 L 320 200" stroke="#e74c3c" stroke-width="4" marker-end="url(#arrowhead2)"/>
                    <text x="285" y="190" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">CARTA</text>
                    <defs>
                        <marker id="arrowhead2" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
                            <polygon points="0 0, 12 4, 0 8" fill="#e74c3c"/>
                        </marker>
                    </defs>

                    <!-- Right side: Differentiation Map -->
                    <g transform="translate(350, 60)">
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Output: Differentiation Map F_S</text>

                        <!-- Progenitor with potency {A,B,C} -->
                        <ellipse cx="150" cy="60" rx="30" ry="18" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                        <text x="150" y="65" text-anchor="middle" font-size="10" fill="white">{A,B,C}</text>
                        <text x="150" y="85" text-anchor="middle" font-size="9" fill="#2c3e50">Totipotent</text>

                        <!-- Intermediate progenitors -->
                        <ellipse cx="100" cy="130" rx="25" ry="15" fill="#f39c12" stroke="#2c3e50" stroke-width="2"/>
                        <text x="100" y="135" text-anchor="middle" font-size="9" fill="white">{A,B}</text>

                        <ellipse cx="200" cy="130" rx="20" ry="15" fill="#f39c12" stroke="#2c3e50" stroke-width="2"/>
                        <text x="200" y="135" text-anchor="middle" font-size="9" fill="white">{C}</text>

                        <!-- Terminal cell types -->
                        <circle cx="70" cy="200" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                        <text x="70" y="205" text-anchor="middle" font-size="10" fill="white">A</text>

                        <circle cx="130" cy="200" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                        <text x="130" y="205" text-anchor="middle" font-size="10" fill="white">B</text>

                        <circle cx="200" cy="200" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                        <text x="200" y="205" text-anchor="middle" font-size="10" fill="white">C</text>

                        <!-- Differentiation edges -->
                        <path d="M 135 75 L 115 115" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead3)"/>
                        <path d="M 165 75 L 185 115" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead3)"/>
                        <path d="M 85 145 L 75 185" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead3)"/>
                        <path d="M 115 145 L 125 185" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead3)"/>
                        <path d="M 200 145 L 200 185" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead3)"/>

                        <defs>
                            <marker id="arrowhead3" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#2c3e50"/>
                            </marker>
                        </defs>
                    </g>

                    <!-- Bottom legend -->
                    <g transform="translate(50, 320)">
                        <text x="300" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Key Components</text>

                        <circle cx="50" cy="40" r="8" fill="#95a5a6"/>
                        <text x="70" y="45" font-size="11" fill="#2c3e50">Unlabeled ancestral cells</text>

                        <circle cx="200" cy="40" r="8" fill="#3498db"/>
                        <text x="220" y="45" font-size="11" fill="#2c3e50">Observed cell types</text>

                        <ellipse cx="370" cy="40" rx="15" ry="8" fill="#e74c3c"/>
                        <text x="395" y="45" font-size="11" fill="#2c3e50">Inferred progenitors (potency sets)</text>

                        <ellipse cx="570" cy="40" rx="15" ry="8" fill="#f39c12"/>
                        <text x="595" y="45" font-size="11" fill="#2c3e50">Intermediate progenitors</text>
                    </g>
                </svg>
            </div>

            <div class="warning-box">
                <h4>⚠️ Key Challenge</h4>
                <p>The main challenge is that ancestral cells in lineage trees are <strong>unlabeled</strong> - we don't know their cell types. CARTA must infer both the progenitor cell types and the differentiation map structure simultaneously.</p>
            </div>
        </section>

        <section id="potency-concept" class="section">
            <h2>🧪 3. The Potency Concept</h2>

            <div class="highlight-box key-concept">
                <h3>What is Potency?</h3>
                <p><strong>Potency</strong> is the set of observed cell types that a progenitor cell can differentiate into. This is CARTA's key innovation for representing unobserved progenitor cell types.</p>
            </div>

            <h3>🔬 Formal Definition</h3>
            <div class="math-formula">
                <p>For a progenitor cell type $p$, its potency is a subset of the observed cell types $S$:</p>
                <p>$$\text{Potency}(p) \subseteq S$$</p>
                <p>where $S = \{A, B, C, \ldots\}$ are the observed cell types</p>
            </div>

            <h3>📋 Examples of Potency</h3>
            <div class="algorithm-box">
                <h4>Potency Examples</h4>
                <ul>
                    <li><strong>Totipotent cell:</strong> Potency = $\{A, B, C\}$ (can become any cell type)</li>
                    <li><strong>Bipotent progenitor:</strong> Potency = $\{A, B\}$ (can become A or B)</li>
                    <li><strong>Unipotent progenitor:</strong> Potency = $\{A\}$ (can only become A)</li>
                    <li><strong>Observed cell type A:</strong> Potency = $\{A\}$ (already differentiated)</li>
                </ul>
            </div>

            <div class="svg-container">
                <svg width="800" height="500" viewBox="0 0 800 500">
                    <!-- Background -->
                    <rect width="800" height="500" fill="url(#bgGrad)" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Potency Hierarchy Visualization</text>

                    <!-- Totipotent level -->
                    <g transform="translate(350, 60)">
                        <ellipse cx="50" cy="30" rx="40" ry="25" fill="#e74c3c" stroke="#2c3e50" stroke-width="3"/>
                        <text x="50" y="35" text-anchor="middle" font-size="12" fill="white" font-weight="bold">{A,B,C,D}</text>
                        <text x="50" y="65" text-anchor="middle" font-size="11" fill="#2c3e50" font-weight="bold">Totipotent</text>
                        <text x="50" y="78" text-anchor="middle" font-size="10" fill="#2c3e50">Highest potency</text>
                    </g>

                    <!-- Multipotent level -->
                    <g transform="translate(200, 140)">
                        <ellipse cx="50" cy="30" rx="35" ry="20" fill="#f39c12" stroke="#2c3e50" stroke-width="2"/>
                        <text x="50" y="35" text-anchor="middle" font-size="11" fill="white">{A,B}</text>
                        <text x="50" y="60" text-anchor="middle" font-size="10" fill="#2c3e50">Bipotent</text>

                        <ellipse cx="200" cy="30" rx="35" ry="20" fill="#f39c12" stroke="#2c3e50" stroke-width="2"/>
                        <text x="200" y="35" text-anchor="middle" font-size="11" fill="white">{C,D}</text>
                        <text x="200" y="60" text-anchor="middle" font-size="10" fill="#2c3e50">Bipotent</text>

                        <ellipse cx="350" cy="30" rx="35" ry="20" fill="#f39c12" stroke="#2c3e50" stroke-width="2"/>
                        <text x="350" y="35" text-anchor="middle" font-size="11" fill="white">{A,C}</text>
                        <text x="350" y="60" text-anchor="middle" font-size="10" fill="#2c3e50">Bipotent</text>
                        <text x="350" y="72" text-anchor="middle" font-size="9" fill="#666">(Convergent)</text>
                    </g>

                    <!-- Unipotent level -->
                    <g transform="translate(100, 240)">
                        <circle cx="50" cy="20" r="18" fill="#27ae60" stroke="#2c3e50" stroke-width="2"/>
                        <text x="50" y="25" text-anchor="middle" font-size="11" fill="white">{A}</text>
                        <text x="50" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Unipotent</text>

                        <circle cx="150" cy="20" r="18" fill="#27ae60" stroke="#2c3e50" stroke-width="2"/>
                        <text x="150" y="25" text-anchor="middle" font-size="11" fill="white">{B}</text>
                        <text x="150" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Unipotent</text>

                        <circle cx="300" cy="20" r="18" fill="#27ae60" stroke="#2c3e50" stroke-width="2"/>
                        <text x="300" y="25" text-anchor="middle" font-size="11" fill="white">{C}</text>
                        <text x="300" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Unipotent</text>

                        <circle cx="450" cy="20" r="18" fill="#27ae60" stroke="#2c3e50" stroke-width="2"/>
                        <text x="450" y="25" text-anchor="middle" font-size="11" fill="white">{D}</text>
                        <text x="450" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Unipotent</text>
                    </g>

                    <!-- Terminal cell types -->
                    <g transform="translate(100, 340)">
                        <circle cx="50" cy="20" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                        <text x="50" y="25" text-anchor="middle" font-size="12" fill="white" font-weight="bold">A</text>
                        <text x="50" y="45" text-anchor="middle" font-size="10" fill="#2c3e50">Observed</text>

                        <circle cx="150" cy="20" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                        <text x="150" y="25" text-anchor="middle" font-size="12" fill="white" font-weight="bold">B</text>
                        <text x="150" y="45" text-anchor="middle" font-size="10" fill="#2c3e50">Observed</text>

                        <circle cx="300" cy="20" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                        <text x="300" y="25" text-anchor="middle" font-size="12" fill="white" font-weight="bold">C</text>
                        <text x="300" y="45" text-anchor="middle" font-size="10" fill="#2c3e50">Observed</text>

                        <circle cx="450" cy="20" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                        <text x="450" y="25" text-anchor="middle" font-size="12" fill="white" font-weight="bold">D</text>
                        <text x="450" y="45" text-anchor="middle" font-size="10" fill="#2c3e50">Observed</text>
                    </g>

                    <!-- Arrows showing differentiation paths -->
                    <!-- From totipotent to bipotent -->
                    <path d="M 380 115 L 270 155" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>
                    <path d="M 420 115 L 380 155" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>
                    <path d="M 420 115 L 530 155" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>

                    <!-- From bipotent to unipotent -->
                    <path d="M 235 190 L 165 250" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>
                    <path d="M 265 190 L 235 250" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>
                    <path d="M 380 190 L 385 250" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>
                    <path d="M 420 190 L 535 250" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>
                    <path d="M 530 190 L 385 250" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>

                    <!-- From unipotent to observed -->
                    <path d="M 150 280 L 150 350" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>
                    <path d="M 250 280 L 250 350" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>
                    <path d="M 400 280 L 400 350" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>
                    <path d="M 550 280 L 550 350" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>

                    <defs>
                        <marker id="arrowhead4" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                            <polygon points="0 0, 8 3, 0 6" fill="#2c3e50"/>
                        </marker>
                    </defs>

                    <!-- Side annotations -->
                    <g transform="translate(50, 120)">
                        <text x="0" y="0" font-size="12" fill="#2c3e50" font-weight="bold">Potency</text>
                        <text x="0" y="15" font-size="12" fill="#2c3e50" font-weight="bold">Decreases</text>
                        <path d="M 20 25 L 20 200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead5)"/>
                        <defs>
                            <marker id="arrowhead5" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                            </marker>
                        </defs>
                    </g>

                    <!-- Legend -->
                    <g transform="translate(50, 420)">
                        <text x="300" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Potency Legend</text>

                        <ellipse cx="50" cy="40" rx="20" ry="12" fill="#e74c3c"/>
                        <text x="80" y="45" font-size="11" fill="#2c3e50">Totipotent (highest potency)</text>

                        <ellipse cx="250" cy="40" rx="20" ry="12" fill="#f39c12"/>
                        <text x="280" y="45" font-size="11" fill="#2c3e50">Multipotent (intermediate)</text>

                        <circle cx="450" cy="40" r="12" fill="#27ae60"/>
                        <text x="470" y="45" font-size="11" fill="#2c3e50">Unipotent (restricted)</text>

                        <circle cx="620" cy="40" r="12" fill="#3498db"/>
                        <text x="640" y="45" font-size="11" fill="#2c3e50">Observed (terminal)</text>
                    </g>
                </svg>
            </div>

            <h3>🎯 Why Potency Matters</h3>
            <div class="highlight-box">
                <h4>Key Advantages of Potency-Based Modeling:</h4>
                <ol>
                    <li><strong>Handles Unobserved Progenitors:</strong> Can represent cell types not captured in experiments</li>
                    <li><strong>Flexible Structure:</strong> Allows both tree and DAG structures (convergent differentiation)</li>
                    <li><strong>Biologically Meaningful:</strong> Directly relates to developmental biology concepts</li>
                    <li><strong>Mathematically Tractable:</strong> Enables optimization algorithms</li>
                </ol>
            </div>

            <div class="math-formula">
                <h4>Potency Constraints</h4>
                <p>For any progenitor $p$ with children $c_1, c_2, \ldots, c_k$:</p>
                <p>$$\text{Potency}(p) \supseteq \bigcup_{i=1}^{k} \text{Potency}(c_i)$$</p>
                <p><em>A progenitor's potency must include all potencies of its descendants</em></p>
            </div>
        </section>

        <section id="algorithm" class="section">
            <h2>⚙️ 4. CARTA Algorithm Overview</h2>

            <div class="highlight-box key-concept">
                <h3>Core Algorithm Strategy</h3>
                <p>CARTA balances two competing objectives:</p>
                <ol>
                    <li><strong>Minimize complexity:</strong> Fewer progenitor cell types</li>
                    <li><strong>Minimize discrepancy:</strong> Better fit to lineage data</li>
                </ol>
            </div>

            <h3>🔄 Algorithm Workflow</h3>
            <div class="algorithm-box">
                <h4>CARTA Algorithm Steps</h4>
                <div style="margin: 1rem 0;">
                    <div style="display: flex; align-items: center; margin: 0.5rem 0;">
                        <span class="step-number">1</span>
                        <span><strong>Input Processing:</strong> Parse lineage trees $T = \{T_1, T_2, \ldots, T_m\}$ and observed cell types $S$</span>
                    </div>
                    <div style="display: flex; align-items: center; margin: 0.5rem 0;">
                        <span class="step-number">2</span>
                        <span><strong>Potency Generation:</strong> Generate all possible potency sets (subsets of $S$)</span>
                    </div>
                    <div style="display: flex; align-items: center; margin: 0.5rem 0;">
                        <span class="step-number">3</span>
                        <span><strong>For each $k$ (number of progenitors):</strong></span>
                    </div>
                    <div style="margin-left: 3rem;">
                        <div style="display: flex; align-items: center; margin: 0.5rem 0;">
                            <span class="step-number">a</span>
                            <span>Select $k$ potency sets to form differentiation map $F_S$</span>
                        </div>
                        <div style="display: flex; align-items: center; margin: 0.5rem 0;">
                            <span class="step-number">b</span>
                            <span>Compute discrepancy score $D(T, F_S)$</span>
                        </div>
                        <div style="display: flex; align-items: center; margin: 0.5rem 0;">
                            <span class="step-number">c</span>
                            <span>Find optimal labeling of ancestral cells</span>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; margin: 0.5rem 0;">
                        <span class="step-number">4</span>
                        <span><strong>Pareto Optimization:</strong> Compute Pareto front of solutions</span>
                    </div>
                    <div style="display: flex; align-items: center; margin: 0.5rem 0;">
                        <span class="step-number">5</span>
                        <span><strong>Solution Selection:</strong> Choose optimal $k^*$ using elbow method</span>
                    </div>
                </div>
            </div>

            <div class="svg-container">
                <svg width="900" height="600" viewBox="0 0 900 600">
                    <!-- Background -->
                    <rect width="900" height="600" fill="url(#bgGrad)" rx="10"/>

                    <!-- Title -->
                    <text x="450" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">CARTA Algorithm Workflow</text>

                    <!-- Step 1: Input -->
                    <g transform="translate(50, 60)">
                        <rect x="0" y="0" width="150" height="80" fill="#3498db" rx="8" opacity="0.9"/>
                        <text x="75" y="25" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Step 1: Input</text>
                        <text x="75" y="40" text-anchor="middle" font-size="10" fill="white">Lineage Trees T</text>
                        <text x="75" y="52" text-anchor="middle" font-size="10" fill="white">Cell Types S</text>
                        <text x="75" y="64" text-anchor="middle" font-size="10" fill="white">S = {A, B, C}</text>
                    </g>

                    <!-- Arrow 1 -->
                    <path d="M 210 100 L 250 100" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead6)"/>

                    <!-- Step 2: Potency Generation -->
                    <g transform="translate(260, 60)">
                        <rect x="0" y="0" width="150" height="80" fill="#f39c12" rx="8" opacity="0.9"/>
                        <text x="75" y="20" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Step 2: Potency</text>
                        <text x="75" y="32" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Generation</text>
                        <text x="75" y="48" text-anchor="middle" font-size="9" fill="white">All subsets of S:</text>
                        <text x="75" y="60" text-anchor="middle" font-size="9" fill="white">{A}, {B}, {C}, {A,B},</text>
                        <text x="75" y="72" text-anchor="middle" font-size="9" fill="white">{A,C}, {B,C}, {A,B,C}</text>
                    </g>

                    <!-- Arrow 2 -->
                    <path d="M 420 100 L 460 100" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead6)"/>

                    <!-- Step 3: Optimization Loop -->
                    <g transform="translate(470, 40)">
                        <rect x="0" y="0" width="180" height="120" fill="#e74c3c" rx="8" opacity="0.9"/>
                        <text x="90" y="20" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Step 3: For each k</text>
                        <text x="90" y="35" text-anchor="middle" font-size="10" fill="white">k = number of progenitors</text>

                        <rect x="10" y="45" width="160" height="65" fill="rgba(255,255,255,0.2)" rx="5"/>
                        <text x="90" y="60" text-anchor="middle" font-size="10" fill="white">a) Select k potency sets</text>
                        <text x="90" y="75" text-anchor="middle" font-size="10" fill="white">b) Compute discrepancy D(T,F_S)</text>
                        <text x="90" y="90" text-anchor="middle" font-size="10" fill="white">c) Optimize labeling</text>
                        <text x="90" y="105" text-anchor="middle" font-size="10" fill="white">d) Store (k, discrepancy)</text>
                    </g>

                    <!-- Arrow 3 -->
                    <path d="M 560 170 L 560 210" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead6)"/>

                    <!-- Step 4: Pareto Front -->
                    <g transform="translate(470, 220)">
                        <rect x="0" y="0" width="180" height="80" fill="#9b59b6" rx="8" opacity="0.9"/>
                        <text x="90" y="25" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Step 4: Pareto Front</text>
                        <text x="90" y="40" text-anchor="middle" font-size="10" fill="white">Compute optimal trade-off</text>
                        <text x="90" y="52" text-anchor="middle" font-size="10" fill="white">between complexity</text>
                        <text x="90" y="64" text-anchor="middle" font-size="10" fill="white">and discrepancy</text>
                    </g>

                    <!-- Arrow 4 -->
                    <path d="M 560 310 L 560 350" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead6)"/>

                    <!-- Step 5: Solution Selection -->
                    <g transform="translate(470, 360)">
                        <rect x="0" y="0" width="180" height="80" fill="#27ae60" rx="8" opacity="0.9"/>
                        <text x="90" y="25" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Step 5: Solution</text>
                        <text x="90" y="37" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Selection</text>
                        <text x="90" y="52" text-anchor="middle" font-size="10" fill="white">Choose optimal k* using</text>
                        <text x="90" y="64" text-anchor="middle" font-size="10" fill="white">elbow method</text>
                    </g>

                    <!-- Side illustration: Pareto Front -->
                    <g transform="translate(50, 200)">
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Pareto Front Illustration</text>

                        <!-- Axes -->
                        <path d="M 50 180 L 50 50" stroke="#2c3e50" stroke-width="2"/>
                        <path d="M 50 180 L 250 180" stroke="#2c3e50" stroke-width="2"/>

                        <!-- Axis labels -->
                        <text x="25" y="115" text-anchor="middle" font-size="11" fill="#2c3e50" transform="rotate(-90, 25, 115)">Discrepancy</text>
                        <text x="150" y="200" text-anchor="middle" font-size="11" fill="#2c3e50">Number of Progenitors (k)</text>

                        <!-- Pareto front curve -->
                        <path d="M 70 160 Q 100 120 130 90 Q 160 70 200 60 Q 230 55 250 52"
                              stroke="#e74c3c" stroke-width="3" fill="none"/>

                        <!-- Points on curve -->
                        <circle cx="70" cy="160" r="4" fill="#e74c3c"/>
                        <circle cx="100" cy="120" r="4" fill="#e74c3c"/>
                        <circle cx="130" cy="90" r="4" fill="#e74c3c"/>
                        <circle cx="160" cy="70" r="4" fill="#e74c3c"/>
                        <circle cx="200" cy="60" r="4" fill="#e74c3c"/>

                        <!-- Optimal point -->
                        <circle cx="130" cy="90" r="6" fill="#27ae60" stroke="#2c3e50" stroke-width="2"/>
                        <text x="130" y="75" text-anchor="middle" font-size="10" fill="#27ae60" font-weight="bold">k*</text>

                        <!-- Labels -->
                        <text x="70" y="175" text-anchor="middle" font-size="9" fill="#2c3e50">k=1</text>
                        <text x="200" y="45" text-anchor="middle" font-size="9" fill="#2c3e50">k=|S|-1</text>
                    </g>

                    <defs>
                        <marker id="arrowhead6" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                        </marker>
                    </defs>

                    <!-- Bottom explanation -->
                    <g transform="translate(50, 480)">
                        <rect x="0" y="0" width="800" height="100" fill="rgba(52, 73, 94, 0.1)" rx="8"/>
                        <text x="400" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Key Insight: Trade-off Optimization</text>

                        <g transform="translate(50, 35)">
                            <circle cx="20" cy="15" r="8" fill="#3498db"/>
                            <text x="35" y="20" font-size="11" fill="#2c3e50">Low k: Simple model, high discrepancy (underfitting)</text>
                        </g>

                        <g transform="translate(450, 35)">
                            <circle cx="20" cy="15" r="8" fill="#e74c3c"/>
                            <text x="35" y="20" font-size="11" fill="#2c3e50">High k: Complex model, low discrepancy (overfitting)</text>
                        </g>

                        <g transform="translate(250, 60)">
                            <circle cx="20" cy="15" r="8" fill="#27ae60"/>
                            <text x="35" y="20" font-size="11" fill="#2c3e50">Optimal k*: Best trade-off (good fit)</text>
                        </g>
                    </g>
                </svg>
            </div>
        </section>

        <section id="discrepancy-score" class="section">
            <h2>📊 5. Discrepancy Score</h2>

            <div class="highlight-box key-concept">
                <h3>What is the Discrepancy Score?</h3>
                <p>The <strong>discrepancy score</strong> $D(T, F_S)$ measures how well a candidate differentiation map $F_S$ fits the observed lineage trees $T$. It counts the minimum number of "missing" cell types when ancestral cells are optimally labeled.</p>
            </div>

            <h3>🔍 Formal Definition</h3>
            <div class="math-formula">
                <h4>Discrepancy Score Formula</h4>
                <p>$$D(T, F_S) = \min_{\ell} \sum_{v \in V_{internal}} \left| \ell(v) \setminus \text{Descendants}(v) \right|$$</p>
                <p>where:</p>
                <ul style="text-align: left; margin-top: 1rem;">
                    <li>$\ell$ is a labeling of internal vertices with potency sets</li>
                    <li>$V_{internal}$ are the internal (ancestral) vertices in trees $T$</li>
                    <li>$\ell(v)$ is the potency assigned to vertex $v$</li>
                    <li>$\text{Descendants}(v)$ are the observed cell types in $v$'s subtree</li>
                </ul>
            </div>

            <h3>🎯 Intuitive Explanation</h3>
            <div class="algorithm-box">
                <h4>How Discrepancy Works</h4>
                <ol>
                    <li><strong>Label ancestral cells:</strong> Assign each ancestral cell a potency set from $F_S$</li>
                    <li><strong>Check descendants:</strong> For each ancestral cell, see what cell types appear in its subtree</li>
                    <li><strong>Count mismatches:</strong> If potency includes cell type X but no X descendants exist, that's a discrepancy</li>
                    <li><strong>Optimize labeling:</strong> Find the labeling that minimizes total discrepancies</li>
                </ol>
            </div>

            <div class="svg-container">
                <svg width="800" height="500" viewBox="0 0 800 500">
                    <!-- Background -->
                    <rect width="800" height="500" fill="url(#bgGrad)" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Discrepancy Score Calculation</text>

                    <!-- Example tree -->
                    <g transform="translate(100, 60)">
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Example: Lineage Tree with Labeling</text>

                        <!-- Root node -->
                        <circle cx="150" cy="60" r="20" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                        <text x="150" y="65" text-anchor="middle" font-size="10" fill="white">{A,B,C}</text>
                        <text x="150" y="90" text-anchor="middle" font-size="9" fill="#2c3e50">Potency: {A,B,C}</text>
                        <text x="150" y="102" text-anchor="middle" font-size="9" fill="#2c3e50">Descendants: {A,B}</text>
                        <text x="150" y="114" text-anchor="middle" font-size="9" fill="#e74c3c" font-weight="bold">Discrepancy: 1 (missing C)</text>

                        <!-- Left child -->
                        <circle cx="80" cy="160" r="18" fill="#f39c12" stroke="#2c3e50" stroke-width="2"/>
                        <text x="80" y="165" text-anchor="middle" font-size="9" fill="white">{A,B}</text>
                        <text x="80" y="185" text-anchor="middle" font-size="9" fill="#2c3e50">Potency: {A,B}</text>
                        <text x="80" y="197" text-anchor="middle" font-size="9" fill="#2c3e50">Descendants: {A,B}</text>
                        <text x="80" y="209" text-anchor="middle" font-size="9" fill="#27ae60" font-weight="bold">Discrepancy: 0 ✓</text>

                        <!-- Right child -->
                        <circle cx="220" cy="160" r="18" fill="#f39c12" stroke="#2c3e50" stroke-width="2"/>
                        <text x="220" y="165" text-anchor="middle" font-size="9" fill="white">{A}</text>
                        <text x="220" y="185" text-anchor="middle" font-size="9" fill="#2c3e50">Potency: {A}</text>
                        <text x="220" y="197" text-anchor="middle" font-size="9" fill="#2c3e50">Descendants: {A}</text>
                        <text x="220" y="209" text-anchor="middle" font-size="9" fill="#27ae60" font-weight="bold">Discrepancy: 0 ✓</text>

                        <!-- Leaf nodes -->
                        <circle cx="50" cy="260" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                        <text x="50" y="265" text-anchor="middle" font-size="10" fill="white">A</text>

                        <circle cx="110" cy="260" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                        <text x="110" y="265" text-anchor="middle" font-size="10" fill="white">B</text>

                        <circle cx="220" cy="260" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                        <text x="220" y="265" text-anchor="middle" font-size="10" fill="white">A</text>

                        <!-- Tree edges -->
                        <path d="M 135 75 L 95 145" stroke="#2c3e50" stroke-width="2"/>
                        <path d="M 165 75 L 205 145" stroke="#2c3e50" stroke-width="2"/>
                        <path d="M 70 175 L 55 245" stroke="#2c3e50" stroke-width="2"/>
                        <path d="M 90 175 L 105 245" stroke="#2c3e50" stroke-width="2"/>
                        <path d="M 220 178 L 220 245" stroke="#2c3e50" stroke-width="2"/>
                    </g>

                    <!-- Calculation breakdown -->
                    <g transform="translate(450, 60)">
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Discrepancy Calculation</text>

                        <rect x="0" y="30" width="300" height="200" fill="rgba(255,255,255,0.8)" stroke="#2c3e50" stroke-width="1" rx="5"/>

                        <text x="150" y="55" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">For each ancestral node:</text>

                        <!-- Root calculation -->
                        <g transform="translate(20, 70)">
                            <text x="0" y="0" font-size="11" fill="#2c3e50" font-weight="bold">Root node:</text>
                            <text x="0" y="15" font-size="10" fill="#2c3e50">• Potency = {A, B, C}</text>
                            <text x="0" y="28" font-size="10" fill="#2c3e50">• Descendants = {A, B}</text>
                            <text x="0" y="41" font-size="10" fill="#2c3e50">• Missing = {C}</text>
                            <text x="0" y="54" font-size="10" fill="#e74c3c" font-weight="bold">• Discrepancy = |{C}| = 1</text>
                        </g>

                        <!-- Left child calculation -->
                        <g transform="translate(20, 135)">
                            <text x="0" y="0" font-size="11" fill="#2c3e50" font-weight="bold">Left child:</text>
                            <text x="0" y="15" font-size="10" fill="#2c3e50">• Potency = {A, B}</text>
                            <text x="0" y="28" font-size="10" fill="#2c3e50">• Descendants = {A, B}</text>
                            <text x="0" y="41" font-size="10" fill="#2c3e50">• Missing = ∅</text>
                            <text x="0" y="54" font-size="10" fill="#27ae60" font-weight="bold">• Discrepancy = 0</text>
                        </g>

                        <!-- Right child calculation -->
                        <g transform="translate(160, 135)">
                            <text x="0" y="0" font-size="11" fill="#2c3e50" font-weight="bold">Right child:</text>
                            <text x="0" y="15" font-size="10" fill="#2c3e50">• Potency = {A}</text>
                            <text x="0" y="28" font-size="10" fill="#2c3e50">• Descendants = {A}</text>
                            <text x="0" y="41" font-size="10" fill="#2c3e50">• Missing = ∅</text>
                            <text x="0" y="54" font-size="10" fill="#27ae60" font-weight="bold">• Discrepancy = 0</text>
                        </g>

                        <!-- Total -->
                        <rect x="20" y="200" width="260" height="25" fill="#34495e" rx="3"/>
                        <text x="150" y="217" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Total Discrepancy D(T, F_S) = 1 + 0 + 0 = 1</text>
                    </g>

                    <!-- Bottom explanation -->
                    <g transform="translate(50, 320)">
                        <text x="350" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Why This Matters</text>

                        <rect x="0" y="30" width="700" height="120" fill="rgba(236, 240, 241, 0.8)" stroke="#bdc3c7" stroke-width="1" rx="8"/>

                        <g transform="translate(30, 50)">
                            <text x="0" y="20" font-size="12" fill="#2c3e50" font-weight="bold">🎯 Lower discrepancy = Better fit to data</text>
                            <text x="0" y="40" font-size="11" fill="#2c3e50">• Discrepancy = 0: Perfect fit (all potencies match descendants)</text>
                            <text x="0" y="55" font-size="11" fill="#2c3e50">• Higher discrepancy: Model doesn't explain the lineage data well</text>
                            <text x="0" y="70" font-size="11" fill="#2c3e50">• CARTA finds the labeling that minimizes total discrepancy</text>

                            <text x="0" y="95" font-size="12" fill="#e74c3c" font-weight="bold">⚠️ Challenge: Sampling limitations can cause discrepancies</text>
                            <text x="0" y="110" font-size="11" fill="#2c3e50">Some cell types in potency may not be sampled → false discrepancies</text>
                        </g>
                    </g>
                </svg>
            </div>

            <div class="math-formula">
                <h4>Normalized Discrepancy</h4>
                <p>To compare across different datasets:</p>
                <p>$$\tilde{D}(T, F_S) = \frac{D(T, F_S)}{\text{Total number of ancestral cells}}$$</p>
            </div>
        </section>

        <section id="pareto-optimization" class="section">
            <h2>⚖️ 6. Pareto Optimization</h2>

            <div class="highlight-box key-concept">
                <h3>The Trade-off Problem</h3>
                <p>CARTA faces a fundamental trade-off:</p>
                <ul>
                    <li><strong>Simple models (low k):</strong> Few progenitors, but high discrepancy</li>
                    <li><strong>Complex models (high k):</strong> Many progenitors, but low discrepancy</li>
                </ul>
                <p><strong>Solution:</strong> Use Pareto optimization to find the best balance</p>
            </div>

            <h3>📈 Pareto Front</h3>
            <div class="math-formula">
                <h4>Pareto Optimality</h4>
                <p>A solution $(k, D)$ is Pareto optimal if there's no other solution $(k', D')$ such that:</p>
                <p>$$k' \leq k \text{ and } D' \leq D \text{ with at least one inequality strict}$$</p>
                <p><em>No solution is strictly better in both objectives</em></p>
            </div>

            <div class="svg-container">
                <svg width="800" height="600" viewBox="0 0 800 600">
                    <!-- Background -->
                    <rect width="800" height="600" fill="url(#bgGrad)" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Pareto Front Visualization</text>

                    <!-- Main plot area -->
                    <g transform="translate(100, 80)">
                        <!-- Axes -->
                        <path d="M 50 350 L 50 50" stroke="#2c3e50" stroke-width="3"/>
                        <path d="M 50 350 L 450 350" stroke="#2c3e50" stroke-width="3"/>

                        <!-- Axis labels -->
                        <text x="25" y="200" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold" transform="rotate(-90, 25, 200)">Discrepancy Score</text>
                        <text x="250" y="380" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Number of Progenitors (k)</text>

                        <!-- Grid lines -->
                        <g stroke="#ecf0f1" stroke-width="1">
                            <path d="M 50 100 L 450 100"/>
                            <path d="M 50 150 L 450 150"/>
                            <path d="M 50 200 L 450 200"/>
                            <path d="M 50 250 L 450 250"/>
                            <path d="M 50 300 L 450 300"/>

                            <path d="M 100 50 L 100 350"/>
                            <path d="M 150 50 L 150 350"/>
                            <path d="M 200 50 L 200 350"/>
                            <path d="M 250 50 L 250 350"/>
                            <path d="M 300 50 L 300 350"/>
                            <path d="M 350 50 L 350 350"/>
                            <path d="M 400 50 L 400 350"/>
                        </g>

                        <!-- Pareto front curve -->
                        <path d="M 80 320 Q 120 250 160 200 Q 200 160 250 130 Q 300 110 350 95 Q 400 85 430 80"
                              stroke="#e74c3c" stroke-width="4" fill="none"/>

                        <!-- Pareto optimal points -->
                        <circle cx="80" cy="320" r="6" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                        <text x="80" y="340" text-anchor="middle" font-size="10" fill="#2c3e50">k=1</text>

                        <circle cx="120" cy="250" r="6" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                        <text x="120" y="270" text-anchor="middle" font-size="10" fill="#2c3e50">k=2</text>

                        <circle cx="160" cy="200" r="6" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                        <text x="160" y="220" text-anchor="middle" font-size="10" fill="#2c3e50">k=3</text>

                        <circle cx="200" cy="160" r="6" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                        <text x="200" y="180" text-anchor="middle" font-size="10" fill="#2c3e50">k=4</text>

                        <circle cx="250" cy="130" r="6" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                        <text x="250" y="150" text-anchor="middle" font-size="10" fill="#2c3e50">k=5</text>

                        <circle cx="300" cy="110" r="6" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                        <text x="300" y="130" text-anchor="middle" font-size="10" fill="#2c3e50">k=6</text>

                        <!-- Optimal solution (elbow) -->
                        <circle cx="200" cy="160" r="10" fill="#27ae60" stroke="#2c3e50" stroke-width="3"/>
                        <text x="200" y="140" text-anchor="middle" font-size="12" fill="#27ae60" font-weight="bold">k* = 4</text>
                        <text x="200" y="125" text-anchor="middle" font-size="10" fill="#27ae60">Optimal</text>

                        <!-- Non-Pareto points (dominated solutions) -->
                        <circle cx="150" cy="280" r="5" fill="#95a5a6" stroke="#2c3e50" stroke-width="1"/>
                        <circle cx="200" cy="250" r="5" fill="#95a5a6" stroke="#2c3e50" stroke-width="1"/>
                        <circle cx="280" cy="200" r="5" fill="#95a5a6" stroke="#2c3e50" stroke-width="1"/>
                        <circle cx="350" cy="180" r="5" fill="#95a5a6" stroke="#2c3e50" stroke-width="1"/>

                        <!-- Axis ticks -->
                        <g font-size="10" fill="#2c3e50">
                            <text x="45" y="355" text-anchor="end">0</text>
                            <text x="45" y="305" text-anchor="end">0.2</text>
                            <text x="45" y="255" text-anchor="end">0.4</text>
                            <text x="45" y="205" text-anchor="end">0.6</text>
                            <text x="45" y="155" text-anchor="end">0.8</text>
                            <text x="45" y="105" text-anchor="end">1.0</text>

                            <text x="100" y="365" text-anchor="middle">1</text>
                            <text x="150" y="365" text-anchor="middle">2</text>
                            <text x="200" y="365" text-anchor="middle">3</text>
                            <text x="250" y="365" text-anchor="middle">4</text>
                            <text x="300" y="365" text-anchor="middle">5</text>
                            <text x="350" y="365" text-anchor="middle">6</text>
                            <text x="400" y="365" text-anchor="middle">7</text>
                        </g>
                    </g>

                    <!-- Legend -->
                    <g transform="translate(550, 120)">
                        <text x="0" y="0" font-size="14" fill="#2c3e50" font-weight="bold">Legend</text>

                        <circle cx="10" cy="25" r="6" fill="#e74c3c"/>
                        <text x="25" y="30" font-size="11" fill="#2c3e50">Pareto optimal solutions</text>

                        <circle cx="10" cy="50" r="6" fill="#27ae60"/>
                        <text x="25" y="55" font-size="11" fill="#2c3e50">Selected optimal solution</text>

                        <circle cx="10" cy="75" r="5" fill="#95a5a6"/>
                        <text x="25" y="80" font-size="11" fill="#2c3e50">Dominated solutions</text>

                        <path d="M 5 100 L 35 100" stroke="#e74c3c" stroke-width="3"/>
                        <text x="45" y="105" font-size="11" fill="#2c3e50">Pareto front</text>
                    </g>

                    <!-- Elbow method explanation -->
                    <g transform="translate(550, 250)">
                        <rect x="0" y="0" width="200" height="120" fill="rgba(255,255,255,0.9)" stroke="#2c3e50" stroke-width="1" rx="5"/>
                        <text x="100" y="20" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">Elbow Method</text>

                        <text x="10" y="40" font-size="10" fill="#2c3e50">1. Compute Pareto front</text>
                        <text x="10" y="55" font-size="10" fill="#2c3e50">2. Find point with maximum</text>
                        <text x="15" y="68" font-size="10" fill="#2c3e50">curvature (elbow)</text>
                        <text x="10" y="83" font-size="10" fill="#2c3e50">3. This balances complexity</text>
                        <text x="15" y="96" font-size="10" fill="#2c3e50">vs. discrepancy optimally</text>
                        <text x="10" y="111" font-size="10" fill="#27ae60" font-weight="bold">4. Select k* = 4</text>
                    </g>

                    <!-- Bottom explanation -->
                    <g transform="translate(50, 480)">
                        <rect x="0" y="0" width="700" height="80" fill="rgba(52, 152, 219, 0.1)" stroke="#3498db" stroke-width="1" rx="8"/>
                        <text x="350" y="25" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Key Insights</text>

                        <g transform="translate(30, 35)">
                            <text x="0" y="15" font-size="11" fill="#2c3e50">• <strong>Left side (low k):</strong> Simple models with high discrepancy (underfitting)</text>
                            <text x="0" y="30" font-size="11" fill="#2c3e50">• <strong>Right side (high k):</strong> Complex models with low discrepancy (overfitting)</text>
                            <text x="0" y="45" font-size="11" fill="#2c3e50">• <strong>Elbow point:</strong> Optimal trade-off between model complexity and data fit</text>
                        </g>
                    </g>
                </svg>
            </div>

            <h3>🔍 Elbow Method for Solution Selection</h3>
            <div class="algorithm-box">
                <h4>Steps to Find Optimal k*</h4>
                <ol>
                    <li><strong>Compute Pareto Front:</strong> For each k = 1, 2, ..., find minimum discrepancy</li>
                    <li><strong>Calculate Curvature:</strong> Find the point with maximum curvature on the Pareto curve</li>
                    <li><strong>Select Elbow:</strong> Choose k* at the elbow point</li>
                    <li><strong>Biological Validation:</strong> Verify that k* makes biological sense</li>
                </ol>
            </div>

            <div class="warning-box">
                <h4>⚠️ Important Considerations</h4>
                <ul>
                    <li><strong>No single "correct" answer:</strong> The Pareto front shows multiple valid trade-offs</li>
                    <li><strong>Domain knowledge matters:</strong> Biological constraints can guide selection</li>
                    <li><strong>Data quality affects results:</strong> Poor sampling can shift the optimal point</li>
                </ul>
            </div>
        </section>

        <section id="implementation" class="section">
            <h2>💻 7. Implementation Steps</h2>

            <div class="highlight-box key-concept">
                <h3>Practical Implementation Guide</h3>
                <p>This section provides a step-by-step guide to implementing CARTA for your own lineage tracing data.</p>
            </div>

            <h3>📋 Prerequisites</h3>
            <div class="algorithm-box">
                <h4>Required Input Data</h4>
                <ul>
                    <li><strong>Lineage Trees:</strong> Cell division trees with unlabeled internal nodes</li>
                    <li><strong>Cell Type Labels:</strong> Observed cell types for leaf nodes</li>
                    <li><strong>Optional:</strong> Timing information (for some variants)</li>
                </ul>
            </div>

            <h3>🔧 Step-by-Step Implementation</h3>

            <div class="algorithm-box">
                <div style="margin: 1rem 0;">
                    <div style="display: flex; align-items: center; margin: 1rem 0;">
                        <span class="step-number">1</span>
                        <div>
                            <strong>Data Preprocessing</strong>
                            <div class="code-block" style="margin-top: 0.5rem;">
# Parse lineage trees and extract cell types
lineage_trees = parse_lineage_data(input_file)
observed_cell_types = extract_cell_types(lineage_trees)
S = set(observed_cell_types)  # Set of observed cell types
                            </div>
                        </div>
                    </div>

                    <div style="display: flex; align-items: center; margin: 1rem 0;">
                        <span class="step-number">2</span>
                        <div>
                            <strong>Generate Potency Sets</strong>
                            <div class="code-block" style="margin-top: 0.5rem;">
# Generate all possible potency sets (subsets of S)
from itertools import combinations

potency_sets = []
for r in range(1, len(S) + 1):
    for subset in combinations(S, r):
        potency_sets.append(set(subset))
                            </div>
                        </div>
                    </div>

                    <div style="display: flex; align-items: center; margin: 1rem 0;">
                        <span class="step-number">3</span>
                        <div>
                            <strong>Discrepancy Calculation Function</strong>
                            <div class="code-block" style="margin-top: 0.5rem;">
def calculate_discrepancy(trees, differentiation_map):
    total_discrepancy = 0

    for tree in trees:
        # Find optimal labeling for this tree
        labeling = optimize_labeling(tree, differentiation_map)

        # Calculate discrepancy for each internal node
        for node in tree.internal_nodes():
            potency = labeling[node]
            descendants = get_descendant_cell_types(node)
            missing = potency - descendants
            total_discrepancy += len(missing)

    return total_discrepancy
                            </div>
                        </div>
                    </div>

                    <div style="display: flex; align-items: center; margin: 1rem 0;">
                        <span class="step-number">4</span>
                        <div>
                            <strong>Pareto Front Computation</strong>
                            <div class="code-block" style="margin-top: 0.5rem;">
def compute_pareto_front(trees, potency_sets, max_k):
    pareto_solutions = []

    for k in range(1, max_k + 1):
        min_discrepancy = float('inf')
        best_map = None

        # Try all combinations of k potency sets
        for combination in combinations(potency_sets, k):
            diff_map = create_differentiation_map(combination)
            discrepancy = calculate_discrepancy(trees, diff_map)

            if discrepancy < min_discrepancy:
                min_discrepancy = discrepancy
                best_map = diff_map

        pareto_solutions.append((k, min_discrepancy, best_map))

    return pareto_solutions
                            </div>
                        </div>
                    </div>

                    <div style="display: flex; align-items: center; margin: 1rem 0;">
                        <span class="step-number">5</span>
                        <div>
                            <strong>Optimal Solution Selection</strong>
                            <div class="code-block" style="margin-top: 0.5rem;">
def find_optimal_k(pareto_solutions):
    # Extract k and discrepancy values
    k_values = [sol[0] for sol in pareto_solutions]
    discrepancies = [sol[1] for sol in pareto_solutions]

    # Find elbow using curvature
    max_curvature = 0
    optimal_k = 1

    for i in range(1, len(k_values) - 1):
        # Calculate curvature at point i
        curvature = calculate_curvature(
            k_values[i-1:i+2],
            discrepancies[i-1:i+2]
        )

        if curvature > max_curvature:
            max_curvature = curvature
            optimal_k = k_values[i]

    return optimal_k
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="svg-container">
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="url(#bgGrad)" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">CARTA Implementation Workflow</text>

                    <!-- Step boxes -->
                    <g transform="translate(50, 60)">
                        <!-- Step 1 -->
                        <rect x="0" y="0" width="120" height="60" fill="#3498db" rx="5" opacity="0.9"/>
                        <text x="60" y="20" text-anchor="middle" font-size="11" fill="white" font-weight="bold">1. Data Prep</text>
                        <text x="60" y="35" text-anchor="middle" font-size="9" fill="white">Parse trees</text>
                        <text x="60" y="47" text-anchor="middle" font-size="9" fill="white">Extract cell types</text>

                        <!-- Arrow -->
                        <path d="M 130 30 L 160 30" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead7)"/>

                        <!-- Step 2 -->
                        <rect x="170" y="0" width="120" height="60" fill="#f39c12" rx="5" opacity="0.9"/>
                        <text x="230" y="20" text-anchor="middle" font-size="11" fill="white" font-weight="bold">2. Potency Sets</text>
                        <text x="230" y="35" text-anchor="middle" font-size="9" fill="white">Generate subsets</text>
                        <text x="230" y="47" text-anchor="middle" font-size="9" fill="white">of observed types</text>

                        <!-- Arrow -->
                        <path d="M 300 30 L 330 30" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead7)"/>

                        <!-- Step 3 -->
                        <rect x="340" y="0" width="120" height="60" fill="#e74c3c" rx="5" opacity="0.9"/>
                        <text x="400" y="20" text-anchor="middle" font-size="11" fill="white" font-weight="bold">3. Discrepancy</text>
                        <text x="400" y="35" text-anchor="middle" font-size="9" fill="white">Calculate fit</text>
                        <text x="400" y="47" text-anchor="middle" font-size="9" fill="white">for each k</text>

                        <!-- Arrow -->
                        <path d="M 470 30 L 500 30" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead7)"/>

                        <!-- Step 4 -->
                        <rect x="510" y="0" width="120" height="60" fill="#9b59b6" rx="5" opacity="0.9"/>
                        <text x="570" y="20" text-anchor="middle" font-size="11" fill="white" font-weight="bold">4. Pareto Front</text>
                        <text x="570" y="35" text-anchor="middle" font-size="9" fill="white">Compute</text>
                        <text x="570" y="47" text-anchor="middle" font-size="9" fill="white">trade-offs</text>

                        <!-- Arrow down -->
                        <path d="M 570 70 L 570 100" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead7)"/>

                        <!-- Step 5 -->
                        <rect x="510" y="110" width="120" height="60" fill="#27ae60" rx="5" opacity="0.9"/>
                        <text x="570" y="130" text-anchor="middle" font-size="11" fill="white" font-weight="bold">5. Select k*</text>
                        <text x="570" y="145" text-anchor="middle" font-size="9" fill="white">Find elbow</text>
                        <text x="570" y="157" text-anchor="middle" font-size="9" fill="white">Optimal solution</text>
                    </g>

                    <defs>
                        <marker id="arrowhead7" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                            <polygon points="0 0, 8 3, 0 6" fill="#2c3e50"/>
                        </marker>
                    </defs>

                    <!-- Code structure illustration -->
                    <g transform="translate(50, 220)">
                        <text x="350" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Main Algorithm Structure</text>

                        <rect x="0" y="30" width="700" height="120" fill="rgba(44, 62, 80, 0.05)" stroke="#2c3e50" stroke-width="1" rx="8"/>

                        <text x="20" y="55" font-size="12" fill="#2c3e50" font-weight="bold">def carta_algorithm(lineage_trees, observed_cell_types):</text>
                        <text x="40" y="75" font-size="11" fill="#2c3e50">1. potency_sets = generate_all_subsets(observed_cell_types)</text>
                        <text x="40" y="90" font-size="11" fill="#2c3e50">2. pareto_front = compute_pareto_front(trees, potency_sets)</text>
                        <text x="40" y="105" font-size="11" fill="#2c3e50">3. optimal_k = find_elbow(pareto_front)</text>
                        <text x="40" y="120" font-size="11" fill="#2c3e50">4. return best_differentiation_map[optimal_k]</text>
                        <text x="40" y="135" font-size="11" fill="#27ae60" font-weight="bold">5. # Returns: Optimal cell differentiation map</text>
                    </g>
                </svg>
            </div>

            <h3>🛠️ Software Tools</h3>
            <div class="highlight-box">
                <h4>Available Implementations</h4>
                <ul>
                    <li><strong>Official CARTA Software:</strong> Available at <a href="https://github.com/raphael-group/CARTA" target="_blank">github.com/raphael-group/CARTA</a></li>
                    <li><strong>Language:</strong> Python with optimization libraries</li>
                    <li><strong>Dependencies:</strong> NetworkX, NumPy, SciPy, Matplotlib</li>
                    <li><strong>Input Format:</strong> Newick trees or custom lineage formats</li>
                </ul>
            </div>

            <div class="warning-box">
                <h4>⚠️ Implementation Challenges</h4>
                <ul>
                    <li><strong>Computational Complexity:</strong> Exponential in number of cell types</li>
                    <li><strong>Memory Requirements:</strong> Can be large for many cell types</li>
                    <li><strong>Parameter Tuning:</strong> May need to adjust for specific datasets</li>
                    <li><strong>Validation:</strong> Results should be validated against biological knowledge</li>
                </ul>
            </div>
        </section>

        <section id="applications" class="section">
            <h2>🧬 8. Real-world Applications</h2>

            <div class="highlight-box key-concept">
                <h3>CARTA Success Stories</h3>
                <p>CARTA has been successfully applied to multiple biological systems, revealing new insights into developmental processes.</p>
            </div>

            <h3>🔬 Application 1: Trunk-Like Structures (TLS)</h3>
            <div class="algorithm-box">
                <h4>Mammalian Trunk Development Model</h4>
                <ul>
                    <li><strong>System:</strong> In vitro embryoid model of mammalian trunk development</li>
                    <li><strong>Key Finding:</strong> Convergent differentiation of somite cells</li>
                    <li><strong>Cell Types:</strong> NMP, Neural Tube, Somite, Endoderm, Endothelial, PGCLC</li>
                    <li><strong>Innovation:</strong> Revealed bipotent nature of neuro-mesodermal progenitors</li>
                </ul>
            </div>

            <h3>🩸 Application 2: Mouse Hematopoiesis</h3>
            <div class="algorithm-box">
                <h4>Blood Cell Development</h4>
                <ul>
                    <li><strong>System:</strong> Mouse hematopoietic stem cell differentiation</li>
                    <li><strong>Key Finding:</strong> Better agreement with canonical hematopoiesis model</li>
                    <li><strong>Cell Types:</strong> Megakaryocytes, Erythrocytes, Mast cells, Basophils, etc.</li>
                    <li><strong>Innovation:</strong> Identified common myeloid progenitor (CMP) pathway</li>
                </ul>
            </div>

            <div class="svg-container">
                <svg width="800" height="500" viewBox="0 0 800 500">
                    <!-- Background -->
                    <rect width="800" height="500" fill="url(#bgGrad)" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">CARTA Applications Overview</text>

                    <!-- TLS Application -->
                    <g transform="translate(50, 60)">
                        <rect x="0" y="0" width="300" height="180" fill="rgba(52, 152, 219, 0.1)" stroke="#3498db" stroke-width="2" rx="10"/>
                        <text x="150" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Trunk-Like Structures</text>

                        <!-- TLS diagram -->
                        <g transform="translate(50, 40)">
                            <!-- Totipotent -->
                            <ellipse cx="100" cy="20" rx="30" ry="15" fill="#e74c3c" opacity="0.8"/>
                            <text x="100" y="25" text-anchor="middle" font-size="9" fill="white">Multipotent</text>

                            <!-- Intermediate progenitors -->
                            <ellipse cx="60" cy="60" rx="25" ry="12" fill="#f39c12" opacity="0.8"/>
                            <text x="60" y="65" text-anchor="middle" font-size="8" fill="white">NMP</text>

                            <ellipse cx="140" cy="60" rx="25" ry="12" fill="#f39c12" opacity="0.8"/>
                            <text x="140" y="65" text-anchor="middle" font-size="8" fill="white">Other</text>

                            <!-- Terminal types -->
                            <circle cx="30" cy="100" r="12" fill="#3498db"/>
                            <text x="30" y="105" text-anchor="middle" font-size="8" fill="white">NT</text>

                            <circle cx="90" cy="100" r="12" fill="#3498db"/>
                            <text x="90" y="105" text-anchor="middle" font-size="8" fill="white">Som</text>

                            <circle cx="140" cy="100" r="12" fill="#3498db"/>
                            <text x="140" y="105" text-anchor="middle" font-size="8" fill="white">End</text>

                            <circle cx="170" cy="100" r="12" fill="#3498db"/>
                            <text x="170" y="105" text-anchor="middle" font-size="8" fill="white">PGC</text>

                            <!-- Connections -->
                            <path d="M 85 30 L 70 48" stroke="#2c3e50" stroke-width="1"/>
                            <path d="M 115 30 L 130 48" stroke="#2c3e50" stroke-width="1"/>
                            <path d="M 50 72 L 35 88" stroke="#2c3e50" stroke-width="1"/>
                            <path d="M 70 72 L 85 88" stroke="#2c3e50" stroke-width="1"/>
                            <path d="M 130 72 L 145 88" stroke="#2c3e50" stroke-width="1"/>
                            <path d="M 150 72 L 165 88" stroke="#2c3e50" stroke-width="1"/>
                        </g>

                        <text x="20" y="150" font-size="11" fill="#2c3e50" font-weight="bold">Key Insights:</text>
                        <text x="20" y="165" font-size="10" fill="#2c3e50">• Convergent somite differentiation</text>
                        <text x="20" y="175" font-size="10" fill="#2c3e50">• NMP bipotency revealed</text>
                    </g>

                    <!-- Hematopoiesis Application -->
                    <g transform="translate(450, 60)">
                        <rect x="0" y="0" width="300" height="180" fill="rgba(231, 76, 60, 0.1)" stroke="#e74c3c" stroke-width="2" rx="10"/>
                        <text x="150" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Mouse Hematopoiesis</text>

                        <!-- Hematopoiesis diagram -->
                        <g transform="translate(50, 40)">
                            <!-- HSC -->
                            <ellipse cx="100" cy="20" rx="25" ry="12" fill="#e74c3c" opacity="0.8"/>
                            <text x="100" y="25" text-anchor="middle" font-size="8" fill="white">HSC</text>

                            <!-- Progenitors -->
                            <ellipse cx="60" cy="60" rx="20" ry="10" fill="#f39c12" opacity="0.8"/>
                            <text x="60" y="65" text-anchor="middle" font-size="8" fill="white">CMP</text>

                            <ellipse cx="140" cy="60" rx="20" ry="10" fill="#f39c12" opacity="0.8"/>
                            <text x="140" y="65" text-anchor="middle" font-size="8" fill="white">MEP</text>

                            <!-- Terminal types -->
                            <circle cx="30" cy="100" r="10" fill="#3498db"/>
                            <text x="30" y="105" text-anchor="middle" font-size="7" fill="white">Neu</text>

                            <circle cx="60" cy="100" r="10" fill="#3498db"/>
                            <text x="60" y="105" text-anchor="middle" font-size="7" fill="white">Mo</text>

                            <circle cx="90" cy="100" r="10" fill="#3498db"/>
                            <text x="90" y="105" text-anchor="middle" font-size="7" fill="white">Ba</text>

                            <circle cx="120" cy="100" r="10" fill="#3498db"/>
                            <text x="120" y="105" text-anchor="middle" font-size="7" fill="white">Meg</text>

                            <circle cx="150" cy="100" r="10" fill="#3498db"/>
                            <text x="150" y="105" text-anchor="middle" font-size="7" fill="white">Ery</text>

                            <!-- Connections -->
                            <path d="M 85 30 L 70 50" stroke="#2c3e50" stroke-width="1"/>
                            <path d="M 115 30 L 130 50" stroke="#2c3e50" stroke-width="1"/>
                            <path d="M 50 70 L 35 90" stroke="#2c3e50" stroke-width="1"/>
                            <path d="M 60 70 L 60 90" stroke="#2c3e50" stroke-width="1"/>
                            <path d="M 70 70 L 85 90" stroke="#2c3e50" stroke-width="1"/>
                            <path d="M 130 70 L 125 90" stroke="#2c3e50" stroke-width="1"/>
                            <path d="M 150 70 L 145 90" stroke="#2c3e50" stroke-width="1"/>
                        </g>

                        <text x="20" y="150" font-size="11" fill="#2c3e50" font-weight="bold">Key Insights:</text>
                        <text x="20" y="165" font-size="10" fill="#2c3e50">• CMP pathway identified</text>
                        <text x="20" y="175" font-size="10" fill="#2c3e50">• Better canonical agreement</text>
                    </g>

                    <!-- Comparison with other methods -->
                    <g transform="translate(50, 280)">
                        <text x="350" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">CARTA vs. Existing Methods</text>

                        <rect x="0" y="30" width="700" height="150" fill="rgba(255,255,255,0.9)" stroke="#bdc3c7" stroke-width="1" rx="8"/>

                        <!-- Table headers -->
                        <text x="50" y="55" font-size="12" fill="#2c3e50" font-weight="bold">Method</text>
                        <text x="200" y="55" font-size="12" fill="#2c3e50" font-weight="bold">Assumptions</text>
                        <text x="400" y="55" font-size="12" fill="#2c3e50" font-weight="bold">Limitations</text>
                        <text x="580" y="55" font-size="12" fill="#2c3e50" font-weight="bold">CARTA Advantage</text>

                        <!-- Separator line -->
                        <path d="M 20 65 L 680 65" stroke="#bdc3c7" stroke-width="1"/>

                        <!-- ICE-FASE row -->
                        <text x="50" y="85" font-size="11" fill="#2c3e50" font-weight="bold">ICE-FASE</text>
                        <text x="200" y="85" font-size="10" fill="#2c3e50">Binary tree structure</text>
                        <text x="400" y="85" font-size="10" fill="#2c3e50">No convergent diff.</text>
                        <text x="580" y="85" font-size="10" fill="#27ae60">Allows DAGs</text>

                        <!-- EvoC row -->
                        <text x="50" y="105" font-size="11" fill="#2c3e50" font-weight="bold">EvoC</text>
                        <text x="200" y="105" font-size="10" fill="#2c3e50">Distance-based</text>
                        <text x="400" y="105" font-size="10" fill="#2c3e50">Fixed k = |S|-1</text>
                        <text x="580" y="105" font-size="10" fill="#27ae60">Optimal k selection</text>

                        <!-- PhyloVelo row -->
                        <text x="50" y="125" font-size="11" fill="#2c3e50" font-weight="bold">PhyloVelo</text>
                        <text x="200" y="125" font-size="10" fill="#2c3e50">All progenitors observed</text>
                        <text x="400" y="125" font-size="10" fill="#2c3e50">Spurious transitions</text>
                        <text x="580" y="125" font-size="10" fill="#27ae60">Handles unobserved</text>

                        <!-- Fitch row -->
                        <text x="50" y="145" font-size="11" fill="#2c3e50" font-weight="bold">Fitch</text>
                        <text x="200" y="145" font-size="10" fill="#2c3e50">Parsimony-based</text>
                        <text x="400" y="145" font-size="10" fill="#2c3e50">No complexity control</text>
                        <text x="580" y="145" font-size="10" fill="#27ae60">Pareto optimization</text>

                        <!-- CARTA row -->
                        <rect x="20" y="155" width="660" height="20" fill="#27ae60" opacity="0.2"/>
                        <text x="50" y="170" font-size="11" fill="#2c3e50" font-weight="bold">CARTA</text>
                        <text x="200" y="170" font-size="10" fill="#2c3e50">Potency-based</text>
                        <text x="400" y="170" font-size="10" fill="#27ae60">Principled trade-offs</text>
                        <text x="580" y="170" font-size="10" fill="#27ae60">All advantages</text>
                    </g>
                </svg>
            </div>

            <h3>📈 Performance Metrics</h3>
            <div class="highlight-box">
                <h4>CARTA Outperforms Existing Methods</h4>
                <ul>
                    <li><strong>Accuracy:</strong> Lower Jaccard distance to ground truth (simulated data)</li>
                    <li><strong>Biological Relevance:</strong> Better agreement with canonical models</li>
                    <li><strong>Flexibility:</strong> Handles both tree and DAG structures</li>
                    <li><strong>Robustness:</strong> Works with limited sampling</li>
                </ul>
            </div>
        </section>

        <section id="limitations" class="section">
            <h2>⚠️ 9. Limitations & Future Work</h2>

            <div class="warning-box">
                <h3>Current Limitations</h3>
                <p>While CARTA represents a significant advance, several limitations present opportunities for future development.</p>
            </div>

            <h3>🔍 Technical Limitations</h3>
            <div class="algorithm-box">
                <h4>Computational Challenges</h4>
                <ol>
                    <li><strong>Exponential Complexity:</strong> Number of potency sets grows as $2^{|S|}$</li>
                    <li><strong>Tree Quality Dependence:</strong> Assumes accurate input lineage trees</li>
                    <li><strong>Maximum Parsimony Framework:</strong> Could benefit from probabilistic models</li>
                    <li><strong>No Dedifferentiation:</strong> Assumes irreversible differentiation</li>
                </ol>
            </div>

            <h3>🧬 Biological Limitations</h3>
            <div class="algorithm-box">
                <h4>Modeling Assumptions</h4>
                <ol>
                    <li><strong>Static Potency:</strong> Doesn't model changing potency over time</li>
                    <li><strong>No Spatial Information:</strong> Ignores cell location and microenvironment</li>
                    <li><strong>Limited to Normal Development:</strong> May not handle cancer/aberrant systems</li>
                    <li><strong>Discrete Cell Types:</strong> Assumes clear cell type boundaries</li>
                </ol>
            </div>

            <div class="svg-container">
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="url(#bgGrad)" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Future Directions</text>

                    <!-- Current CARTA -->
                    <g transform="translate(50, 60)">
                        <rect x="0" y="0" width="200" height="120" fill="rgba(52, 152, 219, 0.2)" stroke="#3498db" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Current CARTA</text>

                        <text x="20" y="50" font-size="11" fill="#2c3e50">✓ Potency modeling</text>
                        <text x="20" y="65" font-size="11" fill="#2c3e50">✓ Pareto optimization</text>
                        <text x="20" y="80" font-size="11" fill="#2c3e50">✓ DAG structures</text>
                        <text x="20" y="95" font-size="11" fill="#2c3e50">✓ Unobserved progenitors</text>
                        <text x="20" y="110" font-size="11" fill="#2c3e50">✓ Lineage tree input</text>
                    </g>

                    <!-- Arrow -->
                    <path d="M 260 120 L 320 120" stroke="#e74c3c" stroke-width="4" marker-end="url(#arrowhead8)"/>
                    <text x="290" y="110" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">Evolve</text>

                    <!-- Future CARTA -->
                    <g transform="translate(330, 60)">
                        <rect x="0" y="0" width="200" height="120" fill="rgba(231, 76, 60, 0.2)" stroke="#e74c3c" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Future CARTA</text>

                        <text x="20" y="50" font-size="11" fill="#2c3e50">+ Probabilistic models</text>
                        <text x="20" y="65" font-size="11" fill="#2c3e50">+ Joint tree inference</text>
                        <text x="20" y="80" font-size="11" fill="#2c3e50">+ Spatial information</text>
                        <text x="20" y="95" font-size="11" fill="#2c3e50">+ Dedifferentiation</text>
                        <text x="20" y="110" font-size="11" fill="#2c3e50">+ Multi-modal data</text>
                    </g>

                    <!-- Extensions -->
                    <g transform="translate(550, 60)">
                        <rect x="0" y="0" width="200" height="120" fill="rgba(39, 174, 96, 0.2)" stroke="#27ae60" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Applications</text>

                        <text x="20" y="50" font-size="11" fill="#2c3e50">• Cancer development</text>
                        <text x="20" y="65" font-size="11" fill="#2c3e50">• Tissue engineering</text>
                        <text x="20" y="80" font-size="11" fill="#2c3e50">• Drug discovery</text>
                        <text x="20" y="95" font-size="11" fill="#2c3e50">• Regenerative medicine</text>
                        <text x="20" y="110" font-size="11" fill="#2c3e50">• Evolutionary biology</text>
                    </g>

                    <defs>
                        <marker id="arrowhead8" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
                        </marker>
                    </defs>

                    <!-- Research priorities -->
                    <g transform="translate(50, 220)">
                        <text x="350" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Priority Research Directions</text>

                        <rect x="0" y="30" width="700" height="120" fill="rgba(255,255,255,0.9)" stroke="#bdc3c7" stroke-width="1" rx="8"/>

                        <g transform="translate(30, 50)">
                            <text x="0" y="20" font-size="12" fill="#e74c3c" font-weight="bold">1. Probabilistic Framework</text>
                            <text x="20" y="35" font-size="10" fill="#2c3e50">Replace maximum parsimony with maximum likelihood models</text>

                            <text x="350" y="20" font-size="12" fill="#e74c3c" font-weight="bold">2. Joint Inference</text>
                            <text x="370" y="35" font-size="10" fill="#2c3e50">Simultaneously infer lineage trees and differentiation maps</text>

                            <text x="0" y="60" font-size="12" fill="#e74c3c" font-weight="bold">3. Multi-modal Integration</text>
                            <text x="20" y="75" font-size="10" fill="#2c3e50">Combine lineage, expression, spatial, and epigenetic data</text>

                            <text x="350" y="60" font-size="12" fill="#e74c3c" font-weight="bold">4. Dynamic Modeling</text>
                            <text x="370" y="75" font-size="10" fill="#2c3e50">Model time-varying potency and dedifferentiation</text>

                            <text x="0" y="100" font-size="12" fill="#e74c3c" font-weight="bold">5. Scalability</text>
                            <text x="20" y="115" font-size="10" fill="#2c3e50">Develop algorithms for larger datasets and more cell types</text>

                            <text x="350" y="100" font-size="12" fill="#e74c3c" font-weight="bold">6. Validation</text>
                            <text x="370" y="115" font-size="10" fill="#2c3e50">Create benchmarks and validation frameworks</text>
                        </g>
                    </g>
                </svg>
            </div>

            <h3>🚀 Opportunities</h3>
            <div class="highlight-box key-concept">
                <h4>Exciting Future Directions</h4>
                <ol>
                    <li><strong>Cancer Research:</strong> Model tumor evolution and metastasis</li>
                    <li><strong>Regenerative Medicine:</strong> Guide stem cell therapies</li>
                    <li><strong>Drug Discovery:</strong> Identify differentiation-modulating compounds</li>
                    <li><strong>Evolutionary Biology:</strong> Study developmental evolution</li>
                    <li><strong>Tissue Engineering:</strong> Design optimal differentiation protocols</li>
                </ol>
            </div>
        </section>

        <footer style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-bg) 100%); color: white; padding: 2rem; text-align: center; margin-top: 2rem; border-radius: 10px;">
            <h3>🎓 Tutorial Complete!</h3>
            <p>You now understand the key concepts and methods behind CARTA for cell differentiation mapping.</p>
            <p style="margin-top: 1rem; opacity: 0.8;">
                <strong>Next Steps:</strong> Try implementing CARTA on your own lineage tracing data or explore the official software at
                <a href="https://github.com/raphael-group/CARTA" style="color: #3498db;">github.com/raphael-group/CARTA</a>
            </p>
            <p style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.7;">
                Based on: "Inferring cell differentiation maps from lineage tracing data" by Sashittal et al., 2024
            </p>
        </footer>
    </div>
</body>
</html>
